using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
   public float paintRadius = 2f;
    public int targetLayerIndex = 0;    // The layer you want to replace (e.g., grass)
    public int applyLayerIndex = 1;     // The new layer to apply (e.g., soil)
    public float touchPosition = 0f;    // Y position offset for terrain touching detection


    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;

    void Start()
    {
        terrain = Terrain.activeTerrain;

        if (terrain != null)
        {
            terrainData = terrain.terrainData;

            // Save original texture map
            originalAlphamaps = terrainData.GetAlphamaps(0, 0,
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }
    }

    void Update()
    {
        if (terrain == null) return;

        // Use object's Y position to check if touching terrain
        Vector3 terrainPos = new Vector3(transform.position.x, 0, transform.position.z);
        float terrainHeight = terrain.SampleHeight(transform.position);

        // Check if object is at or below terrain height (touching)
        if (transform.position.y <= terrainHeight)
        {
            if (IsTargetLayer(terrainPos))
            {
                PaintTextureAt(terrainPos);
            }
        }

        // Reset terrain with R key
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetTerrain();
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int x = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int z = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        float[,,] alpha = terrainData.GetAlphamaps(x, z, 1, 1);
        return alpha[0, 0, targetLayerIndex] >= 0.5f;
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        int radius = Mathf.RoundToInt(paintRadius);
        int size = radius * 2;

        int startX = Mathf.Clamp(mapX - radius, 0, terrainData.alphamapWidth - size);
        int startZ = Mathf.Clamp(mapZ - radius, 0, terrainData.alphamapHeight - size);

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, size, size);

        for (int x = 0; x < size; x++)
        {
            for (int z = 0; z < size; z++)
            {
                float targetWeight = alphaMaps[x, z, targetLayerIndex];
                if (targetWeight >= 0.5f)
                {
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        alphaMaps[x, z, i] = (i == applyLayerIndex) ? 1f : 0f;
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }

    void ResetTerrain()
    {
        terrainData.SetAlphamaps(0, 0, originalAlphamaps);
        Debug.Log("Terrain reset to original textures.");
    }
}
